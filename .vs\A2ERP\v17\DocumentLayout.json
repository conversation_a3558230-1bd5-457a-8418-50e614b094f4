{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC951}|src\\A2ERP.API\\A2ERP.API.csproj|c:\\users\\<USER>\\source\\repos\\a2erp\\src\\a2erp.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC951}|src\\A2ERP.API\\A2ERP.API.csproj|solutionrelative:src\\a2erp.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC951}|src\\A2ERP.API\\A2ERP.API.csproj|c:\\users\\<USER>\\source\\repos\\a2erp\\src\\a2erp.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC951}|src\\A2ERP.API\\A2ERP.API.csproj|solutionrelative:src\\a2erp.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}|src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\a2erp\\src\\modules\\identity\\a2erp.modules.identity.infrastructure\\persistence\\configurations\\roleconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}|src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj|solutionrelative:src\\modules\\identity\\a2erp.modules.identity.infrastructure\\persistence\\configurations\\roleconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC951}|src\\A2ERP.API\\A2ERP.API.csproj|c:\\users\\<USER>\\source\\repos\\a2erp\\src\\a2erp.api\\a2erp.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC951}|src\\A2ERP.API\\A2ERP.API.csproj|solutionrelative:src\\a2erp.api\\a2erp.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC953}|tests\\A2ERP.API.IntegrationTests\\A2ERP.API.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\a2erp\\tests\\a2erp.api.integrationtests\\a2erp.api.integrationtests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC953}|tests\\A2ERP.API.IntegrationTests\\A2ERP.API.IntegrationTests.csproj|solutionrelative:tests\\a2erp.api.integrationtests\\a2erp.api.integrationtests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}|src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\a2erp\\src\\modules\\identity\\a2erp.modules.identity.infrastructure\\services\\jwttokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}|src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj|solutionrelative:src\\modules\\identity\\a2erp.modules.identity.infrastructure\\services\\jwttokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 335, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\appsettings.json", "RelativeDocumentMoniker": "src\\A2ERP.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\appsettings.json", "RelativeToolTip": "src\\A2ERP.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-19T16:52:07.813Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\Program.cs", "RelativeDocumentMoniker": "src\\A2ERP.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\Program.cs", "RelativeToolTip": "src\\A2ERP.API\\Program.cs", "ViewState": "AgIAAKMAAAAA+/////8LwMMAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T16:47:37.125Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "A2ERP.API", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\A2ERP.API.csproj", "RelativeDocumentMoniker": "src\\A2ERP.API\\A2ERP.API.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\A2ERP.API.csproj", "RelativeToolTip": "src\\A2ERP.API\\A2ERP.API.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-19T16:35:19.889Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "RoleConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Persistence\\Configurations\\RoleConfiguration.cs", "RelativeDocumentMoniker": "src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Persistence\\Configurations\\RoleConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Persistence\\Configurations\\RoleConfiguration.cs", "RelativeToolTip": "src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Persistence\\Configurations\\RoleConfiguration.cs", "ViewState": "AgIAACIAAAAAAAAAAAAMwDYAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T16:34:56.34Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "A2ERP.API.IntegrationTests", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\tests\\A2ERP.API.IntegrationTests\\A2ERP.API.IntegrationTests.csproj", "RelativeDocumentMoniker": "tests\\A2ERP.API.IntegrationTests\\A2ERP.API.IntegrationTests.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\tests\\A2ERP.API.IntegrationTests\\A2ERP.API.IntegrationTests.csproj", "RelativeToolTip": "tests\\A2ERP.API.IntegrationTests\\A2ERP.API.IntegrationTests.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-19T13:59:02.695Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "JwtTokenGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Services\\JwtTokenGenerator.cs", "RelativeDocumentMoniker": "src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Services\\JwtTokenGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Services\\JwtTokenGenerator.cs", "RelativeToolTip": "src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\Services\\JwtTokenGenerator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAUwBsAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T13:59:30.264Z", "EditorCaption": ""}]}]}]}