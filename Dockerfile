# Use the official .NET 8 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy csproj files and restore dependencies
COPY src/Shared/A2ERP.SharedKernel/*.csproj ./src/Shared/A2ERP.SharedKernel/
COPY src/Modules/Identity/A2ERP.Modules.Identity.Domain/*.csproj ./src/Modules/Identity/A2ERP.Modules.Identity.Domain/
COPY src/Modules/Identity/A2ERP.Modules.Identity.Application/*.csproj ./src/Modules/Identity/A2ERP.Modules.Identity.Application/
COPY src/Modules/Identity/A2ERP.Modules.Identity.Infrastructure/*.csproj ./src/Modules/Identity/A2ERP.Modules.Identity.Infrastructure/
COPY src/A2ERP.API/*.csproj ./src/A2ERP.API/

RUN dotnet restore ./src/A2ERP.API/A2ERP.API.csproj

# Copy the rest of the source code
COPY . .

# Build the application
RUN dotnet publish src/A2ERP.API/A2ERP.API.csproj -c Release -o out

# Use the official .NET 8 runtime image for running
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app

# Copy the published application
COPY --from=build /app/out .

# Expose the port
EXPOSE 80
EXPOSE 443

# Set the entry point
ENTRYPOINT ["dotnet", "A2ERP.API.dll"]
