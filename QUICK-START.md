# A2ERP Quick Start Guide

## Prerequisites Check

### 1. .NET 8 SDK
```cmd
dotnet --version
```
Should show version 8.0.x or higher.

### 2. SQL Server
Run the connection test:
```cmd
test-sql-connection.cmd
```

If SQL Server is not installed, download from:
- **SQL Server Express** (Free): https://www.microsoft.com/en-us/sql-server/sql-server-downloads
- **SQL Server Developer** (Free): Same link, choose Developer edition

## Quick Setup

### Option 1: Automated Setup (Recommended)
```cmd
# Test SQL Server connection first
test-sql-connection.cmd

# If connection works, run setup
setup-database.cmd

# Start the application
build-and-run.cmd
```

### Option 2: Manual Setup
```cmd
# Build the solution
dotnet build A2ERP.sln

# Create database migration
cd src\A2ERP.API
dotnet ef migrations add InitialCreate --project ..\Modules\Identity\A2ERP.Modules.Identity.Infrastructure\A2ERP.Modules.Identity.Infrastructure.csproj

# Apply migration
dotnet ef database update --project ..\Modules\Identity\A2ERP.Modules.Identity.Infrastructure\A2ERP.Modules.Identity.Infrastructure.csproj

# Run the application
dotnet run
```

## Access the Application

Once running, open your browser to:
- **Swagger UI**: https://localhost:7000/swagger
- **Health Check**: https://localhost:7000/health

## Test the API

### 1. Register a User
```json
POST /api/auth/register
{
  "email": "<EMAIL>",
  "password": "Admin123!",
  "firstName": "Admin",
  "lastName": "User"
}
```

### 2. Login
```json
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "Admin123!"
}
```

### 3. Use the Access Token
Copy the `accessToken` from the login response and use it in the Authorization header:
```
Authorization: Bearer your_access_token_here
```

## Connection String Options

The default connection uses Windows Authentication:
```
Server=.;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true
```

For other options, see `connection-strings-examples.json`

## Troubleshooting

### "No connection could be made" Error
1. Run `test-sql-connection.cmd` to verify SQL Server is accessible
2. Check if SQL Server service is running: `services.msc`
3. Ensure Windows Authentication is enabled in SQL Server

### Migration Errors
1. Delete any existing Migrations folder
2. Run `setup-database.cmd` again
3. Check SQL Server permissions

### Port Already in Use
The application uses ports 7000 (HTTPS) and 5000 (HTTP). If these are in use:
1. Stop other applications using these ports
2. Or modify the ports in `Properties/launchSettings.json`

## Next Steps

- Explore the Swagger UI to see all available endpoints
- Check the `README.md` for detailed architecture information
- Review the `SQL-SERVER-SETUP.md` for advanced SQL Server configurations
- Add new modules following the existing Identity module pattern
