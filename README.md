# A2ERP - Modular ERP System

A comprehensive .NET 8 modular ERP system built with Clean Architecture, CQRS, and Domain-Driven Design principles.

## 🏗️ Architecture

This solution follows Clean Architecture principles with clear separation of concerns:

- **API Layer**: Web API controllers and middleware
- **Application Layer**: CQRS commands/queries, handlers, and DTOs
- **Domain Layer**: Entities, value objects, aggregates, and domain events
- **Infrastructure Layer**: External services and implementations
- **Persistence Layer**: Entity Framework Core, repositories, and database configurations
- **Shared Kernel**: Common domain and application abstractions

## 🚀 Features

### ✅ Implemented
- **Identity Module**: User management, roles, and authentication
- **JWT Authentication**: Access and refresh token support
- **CQRS Pattern**: Command and Query separation with MediatR
- **Domain-Driven Design**: Rich domain models with aggregates and value objects
- **Repository Pattern**: Generic repository with Unit of Work
- **Audit Trail**: Automatic CreatedBy, ModifiedBy, and timestamp tracking
- **Validation**: FluentValidation for command validation
- **Logging**: Structured logging with Serilog
- **Health Checks**: Database and application health monitoring
- **Swagger**: API documentation and testing interface

### 🔄 Ready for Extension
- **Redis Caching**: Infrastructure ready for caching implementation
- **Tax Engine**: Modular structure for tax calculation modules
- **Discount Engine**: Extensible discount calculation system
- **Multi-tenancy**: Database per tenant support

## 🛠️ Technology Stack

- **.NET 8**: Latest .NET framework
- **Entity Framework Core**: ORM with SQL Server support
- **MediatR**: CQRS and mediator pattern implementation
- **FluentValidation**: Input validation
- **AutoMapper**: Object-to-object mapping
- **Scrutor**: Dependency injection scanning
- **Serilog**: Structured logging
- **xUnit**: Unit and integration testing
- **FluentAssertions**: Fluent test assertions

## 📁 Project Structure

```
A2ERP/
├── src/
│   ├── A2ERP.API/                          # Web API layer
│   ├── Shared/
│   │   └── A2ERP.SharedKernel/             # Common domain abstractions
│   └── Modules/
│       └── Identity/
│           ├── A2ERP.Modules.Identity.Domain/        # Domain layer
│           ├── A2ERP.Modules.Identity.Application/   # Application layer
│           └── A2ERP.Modules.Identity.Infrastructure/ # Infrastructure layer
└── tests/
    ├── A2ERP.Modules.Identity.UnitTests/    # Unit tests
    └── A2ERP.API.IntegrationTests/          # Integration tests
```

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- SQL Server LocalDB (included with Visual Studio) or SQL Server Express
- Visual Studio 2022 or VS Code

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd A2ERP
   ```

2. **Configure Database**
   The default connection string uses SQL Server LocalDB:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true"
     }
   }
   ```

3. **Setup Database**
   Run the setup script:
   ```bash
   setup-database.cmd
   ```

   Or manually:
   ```bash
   dotnet ef migrations add InitialCreate --project src/Modules/Identity/A2ERP.Modules.Identity.Infrastructure --startup-project src/A2ERP.API
   dotnet ef database update --project src/Modules/Identity/A2ERP.Modules.Identity.Infrastructure --startup-project src/A2ERP.API
   ```

4. **Run the Application**
   ```bash
   dotnet run --project src/A2ERP.API
   ```

5. **Access Swagger UI**
   Navigate to `https://localhost:7000/swagger` (or the configured port)

## 🔐 Authentication

### Register a New User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

### Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your_refresh_token_here"
}
```

## 🧪 Testing

### Run Unit Tests
```bash
dotnet test tests/A2ERP.Modules.Identity.UnitTests/
```

### Run Integration Tests
```bash
dotnet test tests/A2ERP.API.IntegrationTests/
```

### Run All Tests
```bash
dotnet test
```

## 📊 Health Checks

The application includes health checks accessible at:
- `/health` - Overall application health

## 🔧 Configuration

### JWT Settings
Configure JWT authentication in `appsettings.json`:
```json
{
  "Jwt": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "A2ERP",
    "Audience": "A2ERP-Users",
    "AccessTokenExpirationMinutes": 60,
    "RefreshTokenExpirationDays": 7
  }
}
```

### Logging
Serilog is configured to log to both console and file. Logs are stored in the `logs/` directory.

## 🏗️ Adding New Modules

To add a new module (e.g., Inventory):

1. Create the module structure:
   ```
   src/Modules/Inventory/
   ├── A2ERP.Modules.Inventory.Domain/
   ├── A2ERP.Modules.Inventory.Application/
   └── A2ERP.Modules.Inventory.Infrastructure/
   ```

2. Follow the same patterns as the Identity module
3. Register services in the main API project
4. Add database configurations and migrations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.
