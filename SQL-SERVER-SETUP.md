# SQL Server Setup for A2ERP

## Option 1: SQL Server (Default Configuration)

The application is configured to use a full SQL Server instance with Windows Authentication.

### Connection String (Already configured):
```
Server=.;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true
```

### Steps:
1. Ensure SQL Server is installed and running
2. Run `setup-database.cmd` to create the database
3. Run `build-and-run.cmd` to start the application

## Option 2: SQL Server Express

If you don't have Visual Studio or prefer SQL Server Express:

### Download and Install:
1. Download SQL Server Express from: https://www.microsoft.com/en-us/sql-server/sql-server-downloads
2. Install with default settings

### Update Connection String:
Update `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

## Option 3: SQL Server with SQL Authentication

If you prefer SQL Server Authentication instead of Windows Authentication:

### Update Connection String in appsettings.json:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=A2ERP;User Id=sa;Password=YourPassword123!;MultipleActiveResultSets=true;TrustServerCertificate=true"
  }
}
```

### Enable SQL Server Authentication:
1. Open SQL Server Management Studio
2. Right-click server → Properties → Security
3. Select "SQL Server and Windows Authentication mode"
4. Restart SQL Server service
5. Enable the 'sa' account or create a new SQL login

## Option 4: Docker SQL Server

Use the provided docker-compose.yml:

```bash
docker-compose up sqlserver
```

Then update connection string:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost,1433;Database=A2ERP;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true"
  }
}
```

## Troubleshooting

### "No connection could be made" Error:
1. Ensure SQL Server LocalDB is installed (comes with Visual Studio)
2. Try running: `sqllocaldb start mssqllocaldb`
3. Check if the service is running: `sqllocaldb info mssqllocaldb`

### Migration Issues:
1. Delete the Migrations folder if it exists
2. Run `setup-database.cmd` again
3. Or manually create migration:
   ```bash
   dotnet ef migrations add InitialCreate --project src/Modules/Identity/A2ERP.Modules.Identity.Infrastructure --startup-project src/A2ERP.API
   ```

### Permission Issues:
1. Run Visual Studio or Command Prompt as Administrator
2. Or use SQL Server Authentication instead of Windows Authentication
