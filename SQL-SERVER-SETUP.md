# SQL Server Setup for A2ERP

## Option 1: SQL Server LocalDB (Recommended for Development)

SQL Server LocalDB is included with Visual Studio and is the easiest option for development.

### Connection String (Already configured):
```
Server=(localdb)\mssqllocaldb;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true
```

### Steps:
1. Run `setup-database.cmd` to create the database
2. Run `build-and-run.cmd` to start the application

## Option 2: SQL Server Express

If you don't have Visual Studio or prefer SQL Server Express:

### Download and Install:
1. Download SQL Server Express from: https://www.microsoft.com/en-us/sql-server/sql-server-downloads
2. Install with default settings

### Update Connection String:
Update `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

## Option 3: Full SQL Server

If you have SQL Server installed:

### Update Connection String:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

Or with SQL Server Authentication:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=A2ERP;User Id=your_username;Password=your_password;TrustServerCertificate=true"
  }
}
```

## Option 4: Docker SQL Server

Use the provided docker-compose.yml:

```bash
docker-compose up sqlserver
```

Then update connection string:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost,1433;Database=A2ERP;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true"
  }
}
```

## Troubleshooting

### "No connection could be made" Error:
1. Ensure SQL Server LocalDB is installed (comes with Visual Studio)
2. Try running: `sqllocaldb start mssqllocaldb`
3. Check if the service is running: `sqllocaldb info mssqllocaldb`

### Migration Issues:
1. Delete the Migrations folder if it exists
2. Run `setup-database.cmd` again
3. Or manually create migration:
   ```bash
   dotnet ef migrations add InitialCreate --project src/Modules/Identity/A2ERP.Modules.Identity.Infrastructure --startup-project src/A2ERP.API
   ```

### Permission Issues:
1. Run Visual Studio or Command Prompt as Administrator
2. Or use SQL Server Authentication instead of Windows Authentication
