# A2ERP Startup Troubleshooting Guide

## Issue: Application Hangs at app.Run()

When the application shows all configuration steps as successful but hangs at "About to call app.Run()", try these solutions in order:

### Step 1: Run Diagnosis
```cmd
diagnose-startup.cmd
```
This checks for common issues like port conflicts and certificate problems.

### Step 2: Test HTTP-Only Version
```cmd
test-http-only-version.cmd
```
This eliminates HTTPS certificate issues by running on HTTP only at http://localhost:5000

### Step 3: Test Minimal Version
```cmd
test-minimal-version.cmd
```
This tests with absolutely minimal configuration - no database, no JWT, no custom services.

### Step 4: Fix Development Certificate
```cmd
fix-dev-certificate.cmd
```
This recreates the HTTPS development certificate.

## Common Solutions

### Solution 1: Port Conflicts
Check if ports are in use:
```cmd
netstat -ano | findstr :7000
netstat -ano | findstr :5000
```

If ports are in use, either:
- Kill the processes using those ports
- Use different ports:
  ```cmd
  cd src\A2ERP.API
  dotnet run --urls "http://localhost:8000"
  ```

### Solution 2: HTTPS Certificate Issues
```cmd
# Clean and recreate certificate
dotnet dev-certs https --clean
dotnet dev-certs https --trust

# Or run on HTTP only
cd src\A2ERP.API
dotnet run --urls "http://localhost:5000"
```

### Solution 3: Database Connection Issues
The application might be hanging due to SQL Server connection attempts.

**Option A: Install SQL Server Express**
- Download from: https://www.microsoft.com/en-us/sql-server/sql-server-downloads

**Option B: Use SQL Server LocalDB**
Update connection string in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

**Option C: Test without database**
Use `test-minimal-version.cmd` which skips all database dependencies.

### Solution 4: Firewall/Antivirus
Some antivirus software blocks local web servers:
- Temporarily disable antivirus
- Add exception for dotnet.exe
- Run as administrator

## Expected Successful Output

When working correctly, you should see:
```
🚀 Starting A2ERP API...
Step 1: Creating builder...
Step 2: Configuring Serilog...
✓ Serilog configured successfully
...
Step 13: Starting application...
✅ Application configuration complete!
🔄 About to call app.Run()...
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: https://localhost:7000
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://localhost:5000
```

## Testing if Application is Running

Even if console output stops, the application might be running:

1. **Open browser**: Navigate to https://localhost:7000/test
2. **Use curl**: `curl -k https://localhost:7000/test`
3. **Check processes**: `tasklist | findstr dotnet`

## Quick Commands Reference

```cmd
# Diagnose issues
diagnose-startup.cmd

# Test minimal version (no database/JWT)
test-minimal-version.cmd

# Test HTTP only (no HTTPS certificate)
test-http-only-version.cmd

# Fix HTTPS certificate
fix-dev-certificate.cmd

# Run on different port
cd src\A2ERP.API
dotnet run --urls "http://localhost:8000"

# Check what's using ports
netstat -ano | findstr :7000
netstat -ano | findstr :5000
```
