# A2ERP Troubleshooting Guide

## Issue: "This localhost page can't be found" (HTTP ERROR 404)

This usually means the application isn't running or is running on a different port.

### Step 1: Check if the application is actually running

1. **Try the simple run command:**
   ```cmd
   run-simple.cmd
   ```

2. **Or run manually:**
   ```cmd
   cd src\A2ERP.API
   dotnet run --urls "https://localhost:7000;http://localhost:5000"
   ```

3. **Look for this output:**
   ```
   info: Microsoft.Hosting.Lifetime[14]
         Now listening on: https://localhost:7000
   info: Microsoft.Hosting.Lifetime[14]
         Now listening on: http://localhost:5000
   ```

### Step 2: If the application fails to start

1. **Check for build errors:**
   ```cmd
   dotnet build A2ERP.sln
   ```

2. **Check SQL Server connection:**
   ```cmd
   test-sql-connection.cmd
   ```

3. **Run with detailed debugging:**
   ```cmd
   run-app-debug.cmd
   ```

### Step 3: Common Issues and Solutions

#### Issue: SQL Server Connection Error
```
SocketException: No connection could be made because the target machine actively refused it
```

**Solutions:**
1. Install SQL Server Express: https://www.microsoft.com/en-us/sql-server/sql-server-downloads
2. Start SQL Server service: `services.msc` → Find "SQL Server" services and start them
3. Use SQL Server LocalDB instead:
   ```json
   "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true"
   ```

#### Issue: Migration Errors
```
Unable to create an object of type 'IdentityDbContext'
```

**Solutions:**
1. Run the setup script: `setup-database.cmd`
2. Or create migration manually:
   ```cmd
   cd src\A2ERP.API
   dotnet ef migrations add InitialCreate --project ..\Modules\Identity\A2ERP.Modules.Identity.Infrastructure\A2ERP.Modules.Identity.Infrastructure.csproj
   ```

#### Issue: Port Already in Use
```
Unable to bind to https://localhost:7000
```

**Solutions:**
1. Find what's using the port:
   ```cmd
   netstat -ano | findstr :7000
   ```
2. Kill the process or use different ports:
   ```cmd
   dotnet run --urls "https://localhost:8000;http://localhost:8001"
   ```

#### Issue: Certificate Errors
```
Unable to configure HTTPS endpoint
```

**Solutions:**
1. Trust the development certificate:
   ```cmd
   dotnet dev-certs https --trust
   ```
2. Or run on HTTP only:
   ```cmd
   dotnet run --urls "http://localhost:5000"
   ```

### Step 4: Verify Application is Working

Once the application starts successfully:

1. **Check the console output** for any errors
2. **Navigate to:** https://localhost:7000/swagger
3. **Test the health endpoint:** https://localhost:7000/health
4. **Try registering a user** through Swagger UI

### Step 5: Alternative URLs to Try

If port 7000 doesn't work, the application might be running on:
- http://localhost:5000/swagger
- https://localhost:5001/swagger
- The port shown in the console output

### Step 6: Running Without Database (for testing)

If database issues persist, you can temporarily disable database operations:

1. Comment out the database migration in `Program.cs` (already done)
2. Comment out the health check:
   ```csharp
   // builder.Services.AddHealthChecks()
   //     .AddDbContextCheck<IdentityDbContext>();
   ```
3. Run the application to test if it starts

### Getting Help

If none of these solutions work:

1. **Check the console output** for specific error messages
2. **Look at the logs** in the `logs/` directory
3. **Run with verbose logging:**
   ```cmd
   cd src\A2ERP.API
   dotnet run --verbosity detailed
   ```

## Quick Commands Reference

```cmd
# Test SQL connection
test-sql-connection.cmd

# Setup database
setup-database.cmd

# Run application (simple)
run-simple.cmd

# Run with debugging
run-app-debug.cmd

# Build only
dotnet build A2ERP.sln

# Run manually
cd src\A2ERP.API
dotnet run --urls "https://localhost:7000;http://localhost:5000"
```
