@echo off
echo ========================================
echo A2ERP - Build and Run
echo ========================================

echo.
echo [1/3] Building solution...
dotnet build A2ERP.sln

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo [2/3] Build successful! Starting application...
echo.
echo The API will be available at:
echo - HTTPS: https://localhost:7000/swagger
echo - HTTP:  http://localhost:5000/swagger
echo.
echo Press Ctrl+C to stop the application
echo.

cd src\A2ERP.API
dotnet run

echo.
echo [3/3] Application stopped.
pause
