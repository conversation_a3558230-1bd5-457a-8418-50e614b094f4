@echo off
echo ========================================
echo A2ERP - Build and Run (SQL Server)
echo ========================================

echo.
echo [1/4] Building solution...
dotnet build A2ERP.sln

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo [2/4] Ensuring database is up to date...
cd src\A2ERP.API
dotnet ef database update --context IdentityDbContext --project ..\Modules\Identity\A2ERP.Modules.Identity.Infrastructure\A2ERP.Modules.Identity.Infrastructure.csproj

if %ERRORLEVEL% NEQ 0 (
    echo Database update failed! You may need to run setup-database.cmd first.
    pause
    exit /b 1
)

echo.
echo [3/4] Build successful! Starting application...
echo.
echo The API will be available at:
echo - HTTPS: https://localhost:7000/swagger
echo - HTTP:  http://localhost:5000/swagger
echo.
echo Press Ctrl+C to stop the application
echo.

dotnet run

echo.
echo [4/4] Application stopped.
pause
