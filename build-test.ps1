# PowerShell script to test the build
Write-Host "Starting build test..." -ForegroundColor Green

# Clean the solution
Write-Host "Cleaning solution..." -ForegroundColor Yellow
dotnet clean A2ERP.sln

# Restore packages
Write-Host "Restoring packages..." -ForegroundColor Yellow
dotnet restore A2ERP.sln

# Build the solution
Write-Host "Building solution..." -ForegroundColor Yellow
dotnet build A2ERP.sln --no-restore

Write-Host "Build test completed!" -ForegroundColor Green
