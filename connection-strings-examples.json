{"ConnectionStrings": {"// Default SQL Server (Windows Authentication)": "Server=.;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "// SQL Server with specific instance": "Server=.\\SQLEXPRESS;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "// SQL Server with SQL Authentication": "Server=.;Database=A2ERP;User Id=sa;Password=YourPassword123!;MultipleActiveResultSets=true;TrustServerCertificate=true", "// Remote SQL Server": "Server=your-server-name;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "// SQL Server with specific port": "Server=localhost,1433;Database=A2ERP;User Id=sa;Password=YourPassword123!;MultipleActiveResultSets=true;TrustServerCertificate=true", "// Azure SQL Database": "Server=tcp:your-server.database.windows.net,1433;Initial Catalog=A2ERP;Persist Security Info=False;User ID=your-username;Password=your-password;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}}