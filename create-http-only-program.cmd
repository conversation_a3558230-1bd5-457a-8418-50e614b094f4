@echo off
echo ========================================
echo Creating HTTP-Only Program.cs
echo ========================================

cd src\A2ERP.API

echo.
echo Backing up original Program.cs...
copy Program.cs Program-Original.cs

echo.
echo Creating HTTP-only Program.cs...
(
echo Console.WriteLine("🚀 Starting HTTP-Only A2ERP API..."^);
echo.
echo var builder = WebApplication.CreateBuilder(args^);
echo.
echo Console.WriteLine("Step 1: Adding minimal services..."^);
echo builder.Services.AddControllers(^);
echo builder.Services.AddEndpointsApiExplorer(^);
echo builder.Services.AddSwaggerGen(^);
echo.
echo Console.WriteLine("Step 2: Building app..."^);
echo var app = builder.Build(^);
echo.
echo Console.WriteLine("Step 3: Configuring pipeline..."^);
echo if (app.Environment.IsDevelopment(^^)
echo {
echo     app.UseSwagger(^);
echo     app.UseSwaggerUI(^);
echo }
echo.
echo // Skip HTTPS redirection
echo app.MapControllers(^);
echo.
echo // Add test endpoints
echo app.MapGet("/", (^) =^> "HTTP-Only A2ERP API is running!"^);
echo app.MapGet("/test", (^) =^> new { 
echo     Status = "Running", 
echo     Message = "HTTP-Only API is working",
echo     Timestamp = DateTime.UtcNow 
echo }^);
echo.
echo Console.WriteLine("Step 4: About to start app on HTTP only..."^);
echo Console.WriteLine("Navigate to: http://localhost:5000/test"^);
echo.
echo try
echo {
echo     Console.WriteLine("Calling app.Run(^) with HTTP only..."^);
echo     app.Run("http://localhost:5000"^);
echo }
echo catch (Exception ex^)
echo {
echo     Console.WriteLine($"Error: {ex.Message}"^);
echo     Console.WriteLine($"Stack: {ex.StackTrace}"^);
echo     throw;
echo }
) > Program.cs

echo.
echo HTTP-only Program.cs created successfully!

cd ..\..
