@echo off
echo ========================================
echo Creating Minimal Program.cs
echo ========================================

cd src\A2ERP.API

echo.
echo Backing up original Program.cs...
copy Program.cs Program-Original.cs

echo.
echo Creating minimal Program.cs...
(
echo using Microsoft.AspNetCore.Builder;
echo using Microsoft.Extensions.DependencyInjection;
echo using Microsoft.Extensions.Hosting;
echo.
echo Console.WriteLine("🚀 Starting Minimal A2ERP API..."^);
echo.
echo var builder = WebApplication.CreateBuilder(args^);
echo.
echo Console.WriteLine("Step 1: Adding minimal services..."^);
echo builder.Services.AddControllers(^);
echo builder.Services.AddEndpointsApiExplorer(^);
echo builder.Services.AddSwaggerGen(^);
echo.
echo Console.WriteLine("Step 2: Building app..."^);
echo var app = builder.Build(^);
echo.
echo Console.WriteLine("Step 3: Configuring pipeline..."^);
echo if (app.Environment.IsDevelopment(^^)
echo {
echo     app.UseSwagger(^);
echo     app.UseSwaggerUI(^);
echo }
echo.
echo app.UseHttpsRedirection(^);
echo app.MapControllers(^);
echo.
echo // Add test endpoints
echo app.MapGet("/", (^) =^> "Minimal A2ERP API is running!"^);
echo app.MapGet("/test", (^) =^> new { 
echo     Status = "Running", 
echo     Message = "Minimal API is working",
echo     Timestamp = DateTime.UtcNow 
echo }^);
echo.
echo Console.WriteLine("Step 4: About to start app..."^);
echo Console.WriteLine("Navigate to: https://localhost:7000/test"^);
echo.
echo try
echo {
echo     Console.WriteLine("Calling app.Run(^)..."^);
echo     app.Run("https://localhost:7000"^);
echo }
echo catch (Exception ex^)
echo {
echo     Console.WriteLine($"Error: {ex.Message}"^);
echo     Console.WriteLine($"Stack: {ex.StackTrace}"^);
echo     throw;
echo }
) > Program.cs

echo.
echo Minimal Program.cs created successfully!
echo.
echo To restore original: copy Program-Original.cs Program.cs

cd ..\..
