@echo off
echo ========================================
echo A2ERP Startup Diagnosis
echo ========================================

echo.
echo [1/6] Checking .NET version...
dotnet --version

echo.
echo [2/6] Checking if ports are available...
echo Checking port 7000...
netstat -an | findstr :7000
if %ERRORLEVEL% EQU 0 (
    echo ⚠️ Port 7000 is already in use!
) else (
    echo ✓ Port 7000 is available
)

echo Checking port 5000...
netstat -an | findstr :5000
if %ERRORLEVEL% EQU 0 (
    echo ⚠️ Port 5000 is already in use!
) else (
    echo ✓ Port 5000 is available
)

echo.
echo [3/6] Checking development certificate...
dotnet dev-certs https --check
if %ERRORLEVEL% EQU 0 (
    echo ✓ Development certificate is valid
) else (
    echo ⚠️ Development certificate issue detected
    echo Run: fix-dev-certificate.cmd
)

echo.
echo [4/6] Testing minimal build...
cd src\A2ERP.API
dotnet build --verbosity quiet
if %ERRORLEVEL% EQU 0 (
    echo ✓ Project builds successfully
) else (
    echo ✗ Build failed - check for compilation errors
    cd ..\..
    pause
    exit /b 1
)

echo.
echo [5/6] Checking SQL Server connection...
cd ..\..
call test-sql-connection.cmd

echo.
echo [6/6] Diagnosis complete!
echo.
echo Recommended next steps:
echo 1. If ports are in use: kill the processes or use different ports
echo 2. If certificate issues: run fix-dev-certificate.cmd
echo 3. If SQL issues: install SQL Server or use LocalDB
echo 4. Try minimal version: test-minimal-only.cmd
echo 5. Try HTTP only: test-http-only.cmd

pause
