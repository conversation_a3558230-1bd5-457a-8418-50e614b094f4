version: '3.8'

services:
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: a2erp-sqlserver
    environment:
      SA_PASSWORD: "YourStrong@Passw0rd"
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - a2erp-network

  api:
    build: .
    container_name: a2erp-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=A2ERP;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true
      - Jwt__SecretKey=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - Jwt__Issuer=A2ERP
      - Jwt__Audience=A2ERP-Users
    ports:
      - "8080:80"
      - "8443:443"
    depends_on:
      - sqlserver
    networks:
      - a2erp-network
    volumes:
      - ./logs:/app/logs

volumes:
  sqlserver_data:

networks:
  a2erp-network:
    driver: bridge
