version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: a2erp-postgres
    environment:
      POSTGRES_DB: A2ERP
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - a2erp-network

  api:
    build: .
    container_name: a2erp-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=A2ERP;Username=postgres;Password=postgres
      - Jwt__SecretKey=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - Jwt__Issuer=A2ERP
      - Jwt__Audience=A2ERP-Users
    ports:
      - "8080:80"
      - "8443:443"
    depends_on:
      - postgres
    networks:
      - a2erp-network
    volumes:
      - ./logs:/app/logs

volumes:
  postgres_data:

networks:
  a2erp-network:
    driver: bridge
