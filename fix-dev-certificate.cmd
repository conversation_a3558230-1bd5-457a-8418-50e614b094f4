@echo off
echo ========================================
echo Fixing .NET Development Certificate
echo ========================================

echo.
echo Step 1: Cleaning existing certificates...
dotnet dev-certs https --clean

echo.
echo Step 2: Creating new development certificate...
dotnet dev-certs https --trust

if %ERRORLEVEL% EQU 0 (
    echo ✓ Development certificate created and trusted successfully
) else (
    echo ✗ Failed to create development certificate
    echo This might require administrator privileges
)

echo.
echo Step 3: Checking certificate status...
dotnet dev-certs https --check

echo.
echo Certificate setup completed!
pause
