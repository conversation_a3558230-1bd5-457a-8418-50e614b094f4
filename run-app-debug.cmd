@echo off
echo ========================================
echo A2ERP - Debug Application Startup
echo ========================================

echo.
echo [1/5] Checking .NET version...
dotnet --version

echo.
echo [2/5] Building solution...
dotnet build A2ERP.sln

if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Check the errors above.
    pause
    exit /b 1
)

echo.
echo [3/5] Testing SQL Server connection...
call test-sql-connection.cmd

echo.
echo [4/5] Ensuring database is ready...
cd src\A2ERP.API
dotnet ef database update --project ..\Modules\Identity\A2ERP.Modules.Identity.Infrastructure\A2ERP.Modules.Identity.Infrastructure.csproj --verbose

if %ERRORLEVEL% NEQ 0 (
    echo Database update failed! You may need to create the initial migration first.
    echo Run: setup-database.cmd
    pause
    exit /b 1
)

echo.
echo [5/5] Starting application...
echo.
echo The application will start on:
echo - HTTPS: https://localhost:7000
echo - HTTP:  http://localhost:5000
echo - Swagger: https://localhost:7000/swagger
echo.
echo If the browser doesn't open automatically, navigate to:
echo https://localhost:7000/swagger
echo.

dotnet run --urls "https://localhost:7000;http://localhost:5000"

echo.
echo Application stopped.
pause
