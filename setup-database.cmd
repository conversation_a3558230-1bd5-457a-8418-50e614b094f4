@echo off
echo ========================================
echo A2ERP - Database Setup (SQL Server)
echo ========================================
echo.
echo Make sure SQL Server is running and accessible.
echo Current connection: Server=.;Database=A2ERP
echo.

echo.
echo [1/3] Building solution...
dotnet build A2ERP.sln

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo [2/3] Creating initial migration...
cd src\A2ERP.API
dotnet ef migrations add InitialCreate --context IdentityDbContext --project ..\Modules\Identity\A2ERP.Modules.Identity.Infrastructure\A2ERP.Modules.Identity.Infrastructure.csproj

if %ERRORLEVEL% NEQ 0 (
    echo Migration creation failed!
    pause
    exit /b 1
)

echo.
echo [3/3] Applying migration to database...
dotnet ef database update --context IdentityDbContext --project ..\Modules\Identity\A2ERP.Modules.Identity.Infrastructure\A2ERP.Modules.Identity.Infrastructure.csproj

if %ERRORLEVEL% NEQ 0 (
    echo Database update failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Database setup completed successfully!
echo ========================================
echo.
echo You can now run the application with:
echo   dotnet run
echo.
pause
