<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Scrutor" Version="4.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../Modules/Identity/A2ERP.Modules.Identity.Infrastructure/A2ERP.Modules.Identity.Infrastructure.csproj" />
    <ProjectReference Include="../Modules/Identity/A2ERP.Modules.Identity.Application/A2ERP.Modules.Identity.Application.csproj" />
    <ProjectReference Include="../Shared/A2ERP.SharedKernel/A2ERP.SharedKernel.csproj" />
  </ItemGroup>

</Project>
