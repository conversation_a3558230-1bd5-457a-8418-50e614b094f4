using A2ERP.API.Extensions;
using A2ERP.Modules.Identity.Infrastructure;
using A2ERP.Modules.Identity.Infrastructure.Persistence;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System.Reflection;

Console.WriteLine("Step 1: Creating builder...");
var builder = WebApplication.CreateBuilder(args);

Console.WriteLine("Step 2: Configuring Serilog...");
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

Console.WriteLine("Step 3: Adding basic services...");
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

Console.WriteLine("Step 4: Adding MediatR...");
try
{
    builder.Services.AddMediatR(cfg => {
        cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
        cfg.RegisterServicesFromAssembly(typeof(A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommand).Assembly);
    });
    Console.WriteLine("✓ MediatR configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ MediatR configuration failed: {ex.Message}");
}

Console.WriteLine("Step 5: Adding FluentValidation...");
try
{
    builder.Services.AddValidatorsFromAssembly(typeof(A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommandValidator).Assembly);
    Console.WriteLine("✓ FluentValidation configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ FluentValidation configuration failed: {ex.Message}");
}

Console.WriteLine("Step 6: Adding AutoMapper...");
try
{
    builder.Services.AddAutoMapper(typeof(A2ERP.Modules.Identity.Application.Mappings.IdentityMappingProfile));
    Console.WriteLine("✓ AutoMapper configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ AutoMapper configuration failed: {ex.Message}");
}

Console.WriteLine("Step 7: Adding Identity Infrastructure...");
try
{
    builder.Services.AddIdentityInfrastructure(builder.Configuration);
    Console.WriteLine("✓ Identity Infrastructure configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ Identity Infrastructure configuration failed: {ex.Message}");
}

Console.WriteLine("Step 8: Adding JWT Authentication...");
try
{
    builder.Services.AddJwtAuthentication(builder.Configuration);
    Console.WriteLine("✓ JWT Authentication configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ JWT Authentication configuration failed: {ex.Message}");
}

Console.WriteLine("Step 9: Adding Health Checks...");
try
{
    builder.Services.AddHealthChecks()
        .AddDbContextCheck<IdentityDbContext>();
    Console.WriteLine("✓ Health Checks configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ Health Checks configuration failed: {ex.Message}");
}

Console.WriteLine("Step 10: Adding CORS...");
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

Console.WriteLine("Step 11: Building application...");
var app = builder.Build();

Console.WriteLine("Step 12: Configuring pipeline...");
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

Console.WriteLine("Step 13: Application built successfully!");
Console.WriteLine("Navigate to: https://localhost:7000/swagger");

try
{
    Console.WriteLine("Step 14: Starting application...");
    Log.Information("Starting A2ERP API");
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"✗ Application failed to start: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
