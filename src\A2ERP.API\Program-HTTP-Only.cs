Console.WriteLine("🚀 Starting HTTP-Only A2ERP API Test...");

var builder = WebApplication.CreateBuilder(args);

Console.WriteLine("Step 1: Adding minimal services...");
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

Console.WriteLine("Step 2: Building app...");
var app = builder.Build();

Console.WriteLine("Step 3: Configuring pipeline...");
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Skip HTTPS redirection
app.MapControllers();

// Add test endpoints
app.MapGet("/", () => "HTTP-Only A2ERP API is running!");
app.MapGet("/test", () => new { 
    Status = "Running", 
    Message = "HTTP-Only API is working",
    Timestamp = DateTime.UtcNow 
});

Console.WriteLine("Step 4: About to start app on HTTP only...");
Console.WriteLine("Navigate to: http://localhost:5000/test");

try
{
    Console.WriteLine("Calling app.Run() with HTTP only...");
    app.Run("http://localhost:5000");
}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
    Console.WriteLine($"Stack: {ex.StackTrace}");
    throw;
}
