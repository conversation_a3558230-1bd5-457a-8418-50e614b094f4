Console.WriteLine("🚀 Starting Minimal A2ERP API Test...");

var builder = WebApplication.CreateBuilder(args);

Console.WriteLine("Step 1: Adding minimal services...");
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

Console.WriteLine("Step 2: Building app...");
var app = builder.Build();

Console.WriteLine("Step 3: Configuring pipeline...");
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.MapControllers();

// Add test endpoints
app.MapGet("/", () => "Minimal A2ERP API is running!");
app.MapGet("/test", () => new { 
    Status = "Running", 
    Message = "Minimal API is working",
    Timestamp = DateTime.UtcNow 
});

Console.WriteLine("Step 4: About to start app...");
Console.WriteLine("Navigate to: https://localhost:7000/test");

try
{
    Console.WriteLine("Calling app.Run()...");
    app.Run("https://localhost:7000");
}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
    Console.WriteLine($"Stack: {ex.StackTrace}");
    throw;
}
