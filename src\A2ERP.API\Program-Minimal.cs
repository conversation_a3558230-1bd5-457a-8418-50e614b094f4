var builder = WebApplication.CreateBuilder(args);

// Add minimal services
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure minimal pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.MapControllers();

// Add a simple test endpoint
app.MapGet("/test", () => "A2ERP API is running!");

Console.WriteLine("Starting minimal A2ERP API...");
Console.WriteLine("Navigate to: https://localhost:7000/swagger");
Console.WriteLine("Test endpoint: https://localhost:7000/test");

try
{
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"Error starting application: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    throw;
}
