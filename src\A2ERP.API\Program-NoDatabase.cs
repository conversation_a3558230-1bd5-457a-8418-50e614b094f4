using A2ERP.API.Extensions;
using FluentValidation;
using MediatR;
using Serilog;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add MediatR
builder.Services.AddMediatR(cfg => {
    cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
    cfg.RegisterServicesFromAssembly(typeof(A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommand).Assembly);
});

// Add FluentValidation
builder.Services.AddValidatorsFromAssembly(typeof(A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommandValidator).Assembly);

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(A2ERP.Modules.Identity.Application.Mappings.IdentityMappingProfile));

// Skip Identity Infrastructure (database-related)
// Skip JWT Authentication (depends on database)
// Skip Health Checks (depends on database)

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.MapControllers();

// Add a test endpoint
app.MapGet("/", () => "A2ERP API is running without database!");
app.MapGet("/test", () => new { 
    Status = "Running", 
    Message = "A2ERP API is working without database dependencies",
    Timestamp = DateTime.UtcNow 
});

try
{
    Log.Information("Starting A2ERP API (No Database Mode)");
    Console.WriteLine("A2ERP API starting without database dependencies...");
    Console.WriteLine("Navigate to: https://localhost:7000/swagger");
    Console.WriteLine("Test endpoint: https://localhost:7000/test");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
    Console.WriteLine($"Error: {ex.Message}");
}
finally
{
    Log.CloseAndFlush();
}
