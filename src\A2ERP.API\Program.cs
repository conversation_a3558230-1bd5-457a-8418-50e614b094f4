using A2ERP.API.Extensions;
using A2ERP.Modules.Identity.Infrastructure;
using A2ERP.Modules.Identity.Infrastructure.Persistence;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System.Reflection;

Console.WriteLine("🚀 Starting A2ERP API...");
Console.WriteLine("Step 1: Creating builder...");

var builder = WebApplication.CreateBuilder(args);

Console.WriteLine("Step 2: Configuring Serilog...");
try
{
    // Configure Serilog
    Log.Logger = new LoggerConfiguration()
        .ReadFrom.Configuration(builder.Configuration)
        .Enrich.FromLogContext()
        .WriteTo.Console()
        .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
        .CreateLogger();

    builder.Host.UseSerilog();
    Console.WriteLine("✓ Serilog configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ Serilog configuration failed: {ex.Message}");
    // Continue without Serilog if it fails
}

Console.WriteLine("Step 3: Adding basic services...");
// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
Console.WriteLine("✓ Basic services added");

Console.WriteLine("Step 4: Adding MediatR...");
try
{
    // Add MediatR
    builder.Services.AddMediatR(cfg => {
        cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
        cfg.RegisterServicesFromAssembly(typeof(A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommand).Assembly);
    });
    Console.WriteLine("✓ MediatR configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ MediatR configuration failed: {ex.Message}");
    throw; // This is critical, so we'll stop here
}

Console.WriteLine("Step 5: Adding FluentValidation...");
try
{
    // Add FluentValidation
    builder.Services.AddValidatorsFromAssembly(typeof(A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommandValidator).Assembly);
    Console.WriteLine("✓ FluentValidation configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ FluentValidation configuration failed: {ex.Message}");
    throw;
}

Console.WriteLine("Step 6: Adding AutoMapper...");
try
{
    // Add AutoMapper
    builder.Services.AddAutoMapper(typeof(A2ERP.Modules.Identity.Application.Mappings.IdentityMappingProfile));
    Console.WriteLine("✓ AutoMapper configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ AutoMapper configuration failed: {ex.Message}");
    throw;
}

Console.WriteLine("Step 7: Adding Identity Infrastructure...");
try
{
    // Add Identity Infrastructure
    builder.Services.AddIdentityInfrastructure(builder.Configuration);
    Console.WriteLine("✓ Identity Infrastructure configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ Identity Infrastructure configuration failed: {ex.Message}");
    Console.WriteLine("This might be due to database connection issues.");
    Console.WriteLine("Continuing without Identity Infrastructure for testing...");
    // Don't throw - continue without database for now
}

Console.WriteLine("Step 8: Adding JWT Authentication...");
try
{
    // Add JWT Authentication
    builder.Services.AddJwtAuthentication(builder.Configuration);
    Console.WriteLine("✓ JWT Authentication configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ JWT Authentication configuration failed: {ex.Message}");
    Console.WriteLine("Continuing without JWT Authentication for testing...");
    // Don't throw - continue without JWT for now
}

Console.WriteLine("Step 9: Adding Health Checks...");
try
{
    // Add Health Checks
    builder.Services.AddHealthChecks()
        .AddDbContextCheck<IdentityDbContext>();
    Console.WriteLine("✓ Health Checks configured successfully");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ Health Checks configuration failed: {ex.Message}");
    // Health checks are not critical, so we'll continue
}

Console.WriteLine("Step 10: Adding CORS...");
// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});
Console.WriteLine("✓ CORS configured successfully");

Console.WriteLine("Step 11: Building application...");
var app = builder.Build();
Console.WriteLine("✓ Application built successfully");

Console.WriteLine("Step 12: Configuring HTTP pipeline...");
// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Add a test endpoint
app.MapGet("/", () => "A2ERP API is running!");
app.MapGet("/test", () => new {
    Status = "Running",
    Message = "A2ERP API is working",
    Timestamp = DateTime.UtcNow
});

Console.WriteLine("✓ HTTP pipeline configured successfully");

// Apply migrations (commented out for initial testing)
// using (var scope = app.Services.CreateScope())
// {
//     var context = scope.ServiceProvider.GetRequiredService<IdentityDbContext>();
//     context.Database.Migrate();
// }

Console.WriteLine("Step 13: Starting application...");
Console.WriteLine("🌐 Navigate to: https://localhost:7000/swagger");
Console.WriteLine("🧪 Test endpoint: https://localhost:7000/test");
Console.WriteLine("❤️ Health check: https://localhost:7000/health");

try
{
    Log.Information("Starting A2ERP API");
    Console.WriteLine("✅ Application configuration complete!");
    Console.WriteLine("🔄 About to call app.Run()...");
    Console.WriteLine("🌐 If successful, navigate to: https://localhost:7000/swagger");
    Console.WriteLine("🧪 Test endpoint: https://localhost:7000/test");
    Console.WriteLine("❤️ Health check: https://localhost:7000/health");
    Console.WriteLine("");
    Console.WriteLine("If the application appears to hang here, try opening");
    Console.WriteLine("https://localhost:7000/test in your browser to see if it's actually running.");
    Console.WriteLine("");

    // Try to start the application
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Application failed to start: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    Log.Fatal(ex, "Application terminated unexpectedly");
    throw;
}
finally
{
    Log.CloseAndFlush();
}
