{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=A2ERP;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Jwt": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "A2ERP", "Audience": "A2ERP-Users", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}