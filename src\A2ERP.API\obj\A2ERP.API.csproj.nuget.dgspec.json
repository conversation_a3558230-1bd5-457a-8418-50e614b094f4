{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\A2ERP.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\A2ERP.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\A2ERP.API.csproj", "projectName": "A2ERP.API", "projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\A2ERP.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\A2ERP.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj"}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Identity.Core": {"target": "Package", "version": "[8.0.17, )"}, "Scrutor": {"target": "Package", "version": "[4.2.2, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj", "projectName": "A2ERP.Modules.Identity.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\A2ERP.Modules.Identity.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\A2ERP.Modules.Identity.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\A2ERP.Modules.Identity.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\A2ERP.Modules.Identity.Domain.csproj", "projectName": "A2ERP.Modules.Identity.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\A2ERP.Modules.Identity.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj", "projectName": "A2ERP.Modules.Identity.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\A2ERP.Modules.Identity.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\A2ERP.Modules.Identity.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Domain\\A2ERP.Modules.Identity.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}, "Scrutor": {"target": "Package", "version": "[4.2.2, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj", "projectName": "A2ERP.SharedKernel", "projectPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MediatR": {"target": "Package", "version": "[12.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}