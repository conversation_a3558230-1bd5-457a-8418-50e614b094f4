[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_2", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain"], "StatusCode": 200}]}, {"ContainingType": "A2ERP.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "A2ERP.Modules.Identity.Application.Commands.LoginUser.LoginUserCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "A2ERP.Modules.Identity.Application.Commands.RefreshToken.RefreshTokenCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.UsersController", "Method": "GetById", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.UsersController", "Method": "GetCurrentUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_3", "RelativePath": "test", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}]