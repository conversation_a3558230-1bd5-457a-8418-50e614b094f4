[{"ContainingType": "A2ERP.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "A2ERP.Modules.Identity.Application.Commands.LoginUser.LoginUserCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "A2ERP.Modules.Identity.Application.Commands.RefreshToken.RefreshTokenCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "A2ERP.Modules.Identity.Application.Commands.RegisterUser.RegisterUserCommand", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.UsersController", "Method": "GetById", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "A2ERP.API.Controllers.UsersController", "Method": "GetCurrentUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]