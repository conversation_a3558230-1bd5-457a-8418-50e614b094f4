{"openapi": "3.0.1", "info": {"title": "A2ERP.API", "version": "1.0"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterUserCommand"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginUserCommand"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenCommand"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"LoginUserCommand": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RefreshTokenCommand": {"type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterUserCommand": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}