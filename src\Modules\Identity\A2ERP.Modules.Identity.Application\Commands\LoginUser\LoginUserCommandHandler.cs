using A2ERP.SharedKernel.Application;
using A2ERP.SharedKernel.Domain;
using A2ERP.Modules.Identity.Application.DTOs;
using A2ERP.Modules.Identity.Application.Services;
using A2ERP.Modules.Identity.Domain.Repositories;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using AutoMapper;

namespace A2ERP.Modules.Identity.Application.Commands.LoginUser;

public sealed class LoginUserCommandHandler : ICommandHandler<LoginUserCommand, Result<AuthenticationResult>>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IJwtTokenGenerator _jwtTokenGenerator;
    private readonly IMapper _mapper;

    public LoginUserCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        IJwtTokenGenerator jwtTokenGenerator,
        IMapper mapper)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _passwordHasher = passwordHasher;
        _jwtTokenGenerator = jwtTokenGenerator;
        _mapper = mapper;
    }

    public async Task<Result<AuthenticationResult>> Handle(LoginUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var email = Email.Create(request.Email);

            // Get user by email
            var user = await _userRepository.GetByEmailAsync(email, cancellationToken);
            if (user is null)
            {
                return Result.Failure<AuthenticationResult>(new Error("Auth.InvalidCredentials", "Invalid email or password."));
            }

            // Check if user is active
            if (!user.IsActive)
            {
                return Result.Failure<AuthenticationResult>(new Error("Auth.UserInactive", "User account is inactive."));
            }

            // Verify password
            if (!_passwordHasher.VerifyPassword(request.Password, user.PasswordHash))
            {
                return Result.Failure<AuthenticationResult>(new Error("Auth.InvalidCredentials", "Invalid email or password."));
            }

            // Generate tokens
            var accessToken = _jwtTokenGenerator.GenerateAccessToken(user);
            var refreshToken = _jwtTokenGenerator.GenerateRefreshToken();
            var expiresAt = DateTime.UtcNow.AddMinutes(_jwtTokenGenerator.AccessTokenExpirationMinutes);

            // Update user with refresh token and last login
            user.SetRefreshToken(refreshToken, DateTime.UtcNow.AddDays(_jwtTokenGenerator.RefreshTokenExpirationDays));
            user.UpdateLastLogin();

            // Save changes
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Map user to DTO
            var userDto = _mapper.Map<UserDto>(user);

            var authResult = new AuthenticationResult(accessToken, refreshToken, expiresAt, userDto);

            return Result.Success(authResult);
        }
        catch (ArgumentException ex)
        {
            return Result.Failure<AuthenticationResult>(new Error("Auth.ValidationError", ex.Message));
        }
        catch (Exception ex)
        {
            return Result.Failure<AuthenticationResult>(new Error("Auth.UnexpectedError", "An unexpected error occurred during authentication."));
        }
    }
}
