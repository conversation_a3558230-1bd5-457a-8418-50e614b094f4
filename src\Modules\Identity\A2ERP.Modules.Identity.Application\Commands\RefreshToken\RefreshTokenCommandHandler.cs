using A2ERP.SharedKernel.Application;
using A2ERP.SharedKernel.Domain;
using A2ERP.Modules.Identity.Application.DTOs;
using A2ERP.Modules.Identity.Application.Services;
using A2ERP.Modules.Identity.Domain.Repositories;
using AutoMapper;

namespace A2ERP.Modules.Identity.Application.Commands.RefreshToken;

public sealed class RefreshTokenCommandHandler : ICommandHandler<RefreshTokenCommand, Result<AuthenticationResult>>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IJwtTokenGenerator _jwtTokenGenerator;
    private readonly IMapper _mapper;

    public RefreshTokenCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        IJwtTokenGenerator jwtTokenGenerator,
        IMapper mapper)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _jwtTokenGenerator = jwtTokenGenerator;
        _mapper = mapper;
    }

    public async Task<Result<AuthenticationResult>> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Find user by refresh token
            var user = await _userRepository.GetByRefreshTokenAsync(request.RefreshToken, cancellationToken);
            if (user is null)
            {
                return Result.Failure<AuthenticationResult>(new Error("Auth.InvalidRefreshToken", "Invalid or expired refresh token."));
            }

            // Check if user is active
            if (!user.IsActive)
            {
                return Result.Failure<AuthenticationResult>(new Error("Auth.UserInactive", "User account is inactive."));
            }

            // Generate new tokens
            var accessToken = _jwtTokenGenerator.GenerateAccessToken(user);
            var newRefreshToken = _jwtTokenGenerator.GenerateRefreshToken();
            var expiresAt = DateTime.UtcNow.AddMinutes(_jwtTokenGenerator.AccessTokenExpirationMinutes);

            // Update user with new refresh token
            user.SetRefreshToken(newRefreshToken, DateTime.UtcNow.AddDays(_jwtTokenGenerator.RefreshTokenExpirationDays));

            // Save changes
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Map user to DTO
            var userDto = _mapper.Map<UserDto>(user);

            var authResult = new AuthenticationResult(accessToken, newRefreshToken, expiresAt, userDto);

            return Result.Success(authResult);
        }
        catch (Exception ex)
        {
            return Result.Failure<AuthenticationResult>(new Error("Auth.UnexpectedError", "An unexpected error occurred during token refresh."));
        }
    }
}
