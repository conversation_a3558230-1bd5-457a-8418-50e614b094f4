using A2ERP.SharedKernel.Application;
using A2ERP.SharedKernel.Domain;
using A2ERP.Modules.Identity.Application.DTOs;
using A2ERP.Modules.Identity.Application.Services;
using A2ERP.Modules.Identity.Domain.Entities;
using A2ERP.Modules.Identity.Domain.Repositories;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using AutoMapper;

namespace A2ERP.Modules.Identity.Application.Commands.RegisterUser;

public sealed class RegisterUserCommandHandler : ICommandHandler<RegisterUserCommand, Result<UserDto>>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IMapper _mapper;

    public RegisterUserCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        IMapper mapper)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _passwordHasher = passwordHasher;
        _mapper = mapper;
    }

    public async Task<Result<UserDto>> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var email = Email.Create(request.Email);

            // Check if user already exists
            if (await _userRepository.ExistsByEmailAsync(email, cancellationToken))
            {
                return Result.Failure<UserDto>(new Error("User.EmailExists", "A user with this email already exists."));
            }

            // Hash the password
            var passwordHash = _passwordHasher.HashPassword(request.Password);

            // Create the user
            var user = User.Create(email, request.FirstName, request.LastName, passwordHash);

            // Add to repository
            _userRepository.Add(user);

            // Save changes
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Map to DTO
            var userDto = _mapper.Map<UserDto>(user);

            return Result.Success(userDto);
        }
        catch (ArgumentException ex)
        {
            return Result.Failure<UserDto>(new Error("User.ValidationError", ex.Message));
        }
        catch (Exception ex)
        {
            return Result.Failure<UserDto>(new Error("User.UnexpectedError", "An unexpected error occurred while registering the user."));
        }
    }
}
