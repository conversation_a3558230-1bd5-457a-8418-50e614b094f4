using AutoMapper;
using A2ERP.Modules.Identity.Application.DTOs;
using A2ERP.Modules.Identity.Domain.Entities;

namespace A2ERP.Modules.Identity.Application.Mappings;

public sealed class IdentityMappingProfile : Profile
{
    public IdentityMappingProfile()
    {
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.Value))
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email.Value))
            .ForMember(dest => dest.Roles, opt => opt.MapFrom(src => new List<RoleDto>())); // Empty list for now

        CreateMap<Role, RoleDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.Value))
            .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => src.Permissions));
    }
}
