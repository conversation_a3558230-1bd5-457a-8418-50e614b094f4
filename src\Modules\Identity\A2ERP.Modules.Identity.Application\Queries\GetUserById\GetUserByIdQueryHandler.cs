using A2ERP.SharedKernel.Application;
using A2ERP.Modules.Identity.Application.DTOs;
using A2ERP.Modules.Identity.Domain.Repositories;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using AutoMapper;

namespace A2ERP.Modules.Identity.Application.Queries.GetUserById;

public sealed class GetUserByIdQueryHandler : IQueryHandler<GetUserByIdQuery, Result<UserDto>>
{
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;

    public GetUserByIdQueryHandler(IUserRepository userRepository, IMapper mapper)
    {
        _userRepository = userRepository;
        _mapper = mapper;
    }

    public async Task<Result<UserDto>> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var userId = UserId.Create(request.UserId);
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);

            if (user is null)
            {
                return Result.Failure<UserDto>(new Error("User.NotFound", "User not found."));
            }

            var userDto = _mapper.Map<UserDto>(user);
            return Result.Success(userDto);
        }
        catch (ArgumentException ex)
        {
            return Result.Failure<UserDto>(new Error("User.ValidationError", ex.Message));
        }
        catch (Exception ex)
        {
            return Result.Failure<UserDto>(new Error("User.UnexpectedError", "An unexpected error occurred while retrieving the user."));
        }
    }
}
