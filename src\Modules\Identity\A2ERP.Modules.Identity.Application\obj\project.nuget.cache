{"version": 2, "dgSpecHash": "5rxEzV3bgO0=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Modules\\Identity\\A2ERP.Modules.Identity.Application\\A2ERP.Modules.Identity.Application.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper.extensions.microsoft.dependencyinjection\\12.0.1\\automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.9.0\\fluentvalidation.11.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation.dependencyinjectionextensions\\11.9.0\\fluentvalidation.dependencyinjectionextensions.11.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\12.2.0\\mediatr.12.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.contracts\\2.0.1\\mediatr.contracts.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\6.0.0\\microsoft.extensions.options.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\6.0.0\\microsoft.extensions.primitives.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"], "logs": []}