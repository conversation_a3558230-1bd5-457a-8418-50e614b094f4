using A2ERP.SharedKernel.Domain;
using A2ERP.Modules.Identity.Domain.ValueObjects;

namespace A2ERP.Modules.Identity.Domain.Entities;

public sealed class Role : AuditableEntity<RoleId>
{
    private readonly List<string> _permissions = new();

    private Role(RoleId id, string name, string? description = null)
        : base(id)
    {
        Name = name;
        Description = description;
        IsActive = true;
    }

    private Role() : base()
    {
    }

    public string Name { get; private set; } = string.Empty;
    public string? Description { get; private set; }
    public bool IsActive { get; private set; }

    public IReadOnlyList<string> Permissions => _permissions.AsReadOnly();

    public static Role Create(string name, string? description = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Role name cannot be null or empty.", nameof(name));

        var roleId = RoleId.CreateUnique();
        return new Role(roleId, name, description);
    }

    public void UpdateDetails(string name, string? description = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Role name cannot be null or empty.", nameof(name));

        Name = name;
        Description = description;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void AddPermission(string permission)
    {
        if (string.IsNullOrWhiteSpace(permission))
            throw new ArgumentException("Permission cannot be null or empty.", nameof(permission));

        if (!_permissions.Contains(permission))
        {
            _permissions.Add(permission);
        }
    }

    public void RemovePermission(string permission)
    {
        _permissions.Remove(permission);
    }

    public bool HasPermission(string permission)
    {
        return _permissions.Contains(permission);
    }

    public void SetPermissions(IEnumerable<string> permissions)
    {
        _permissions.Clear();
        _permissions.AddRange(permissions.Where(p => !string.IsNullOrWhiteSpace(p)));
    }
}
