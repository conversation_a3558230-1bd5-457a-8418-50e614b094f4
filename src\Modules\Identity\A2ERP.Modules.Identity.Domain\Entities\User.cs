using A2ERP.SharedKernel.Domain;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using A2ERP.Modules.Identity.Domain.Events;

namespace A2ERP.Modules.Identity.Domain.Entities;

public sealed class User : AuditableEntity<UserId>
{
    private readonly List<RoleId> _roleIds = new();

    private User(UserId id, Email email, string firstName, string lastName, string passwordHash)
        : base(id)
    {
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        PasswordHash = passwordHash;
        IsActive = true;
        EmailConfirmed = false;
    }

    private User() : base()
    {
    }

    public Email Email { get; private set; } = null!;
    public string FirstName { get; private set; } = string.Empty;
    public string LastName { get; private set; } = string.Empty;
    public string PasswordHash { get; private set; } = string.Empty;
    public bool IsActive { get; private set; }
    public bool EmailConfirmed { get; private set; }
    public DateTime? LastLoginAt { get; private set; }
    public string? RefreshToken { get; private set; }
    public DateTime? RefreshTokenExpiryTime { get; private set; }

    public IReadOnlyList<RoleId> RoleIds => _roleIds.AsReadOnly();

    public static User Create(
        Email email,
        string firstName,
        string lastName,
        string passwordHash)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be null or empty.", nameof(firstName));

        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be null or empty.", nameof(lastName));

        if (string.IsNullOrWhiteSpace(passwordHash))
            throw new ArgumentException("Password hash cannot be null or empty.", nameof(passwordHash));

        var userId = UserId.CreateUnique();
        var user = new User(userId, email, firstName, lastName, passwordHash);

        user.RaiseDomainEvent(new UserCreatedDomainEvent(userId, email, firstName, lastName));

        return user;
    }

    public void ChangeEmail(Email newEmail)
    {
        if (Email == newEmail)
            return;

        var oldEmail = Email;
        Email = newEmail;
        EmailConfirmed = false;

        RaiseDomainEvent(new UserEmailChangedDomainEvent(Id, oldEmail, newEmail));
    }

    public void UpdateProfile(string firstName, string lastName)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be null or empty.", nameof(firstName));

        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be null or empty.", nameof(lastName));

        FirstName = firstName;
        LastName = lastName;
    }

    public void ChangePassword(string newPasswordHash)
    {
        if (string.IsNullOrWhiteSpace(newPasswordHash))
            throw new ArgumentException("Password hash cannot be null or empty.", nameof(newPasswordHash));

        PasswordHash = newPasswordHash;
    }

    public void ConfirmEmail()
    {
        EmailConfirmed = true;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void UpdateLastLogin()
    {
        LastLoginAt = DateTime.UtcNow;
    }

    public void SetRefreshToken(string refreshToken, DateTime expiryTime)
    {
        RefreshToken = refreshToken;
        RefreshTokenExpiryTime = expiryTime;
    }

    public void ClearRefreshToken()
    {
        RefreshToken = null;
        RefreshTokenExpiryTime = null;
    }

    public void AddRole(RoleId roleId)
    {
        if (!_roleIds.Contains(roleId))
        {
            _roleIds.Add(roleId);
        }
    }

    public void RemoveRole(RoleId roleId)
    {
        _roleIds.Remove(roleId);
    }

    public bool HasRole(RoleId roleId)
    {
        return _roleIds.Contains(roleId);
    }
}
