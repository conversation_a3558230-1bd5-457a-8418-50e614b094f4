using A2ERP.SharedKernel.Domain;
using A2ERP.Modules.Identity.Domain.Entities;
using A2ERP.Modules.Identity.Domain.ValueObjects;

namespace A2ERP.Modules.Identity.Domain.Repositories;

public interface IRoleRepository : IRepository<Role, RoleId>
{
    Task<Role?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<Role>> GetActiveRolesAsync(CancellationToken cancellationToken = default);
}
