using A2ERP.SharedKernel.Domain;
using A2ERP.Modules.Identity.Domain.Entities;
using A2ERP.Modules.Identity.Domain.ValueObjects;

namespace A2ERP.Modules.Identity.Domain.Repositories;

public interface IUserRepository : IRepository<User, UserId>
{
    Task<User?> GetByEmailAsync(Email email, CancellationToken cancellationToken = default);
    Task<bool> ExistsByEmailAsync(Email email, CancellationToken cancellationToken = default);
    Task<User?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetByRoleIdAsync(RoleId roleId, CancellationToken cancellationToken = default);
}
