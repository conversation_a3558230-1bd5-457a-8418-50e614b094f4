using A2ERP.SharedKernel.Domain;

namespace A2ERP.Modules.Identity.Domain.ValueObjects;

public sealed class RoleId : ValueObject
{
    public Guid Value { get; }

    private RoleId(Guid value)
    {
        Value = value;
    }

    public static RoleId Create(Guid value)
    {
        if (value == Guid.Empty)
        {
            throw new ArgumentException("RoleId cannot be empty.", nameof(value));
        }

        return new RoleId(value);
    }

    public static RoleId CreateUnique() => new(Guid.NewGuid());

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }

    public override string ToString() => Value.ToString();

    public static implicit operator Guid(RoleId roleId) => roleId.Value;
    public static implicit operator RoleId(Guid value) => Create(value);
}
