<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Scrutor" Version="4.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../A2ERP.Modules.Identity.Application/A2ERP.Modules.Identity.Application.csproj" />
    <ProjectReference Include="../A2ERP.Modules.Identity.Domain/A2ERP.Modules.Identity.Domain.csproj" />
    <ProjectReference Include="../../../Shared/A2ERP.SharedKernel/A2ERP.SharedKernel.csproj" />
  </ItemGroup>

</Project>
