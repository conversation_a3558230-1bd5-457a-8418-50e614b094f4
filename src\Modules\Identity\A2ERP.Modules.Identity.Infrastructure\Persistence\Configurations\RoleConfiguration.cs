using A2ERP.Modules.Identity.Domain.Entities;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace A2ERP.Modules.Identity.Infrastructure.Persistence.Configurations;

public sealed class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.ToTable("Roles", "Identity");

        builder.HasKey(r => r.Id);

        builder.Property(r => r.Id)
            .HasConversion(
                id => id.Value,
                value => RoleId.Create(value))
            .ValueGeneratedNever();

        builder.Property(r => r.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.HasIndex(r => r.Name)
            .IsUnique()
            .HasDatabaseName("IX_Roles_Name");

        builder.Property(r => r.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(r => r.IsActive)
            .IsRequired();

        // Audit properties
        builder.Property(r => r.CreatedAt)
            .IsRequired();

        builder.Property(r => r.CreatedBy)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(r => r.ModifiedAt)
            .IsRequired(false);

        builder.Property(r => r.ModifiedBy)
            .HasMaxLength(255)
            .IsRequired(false);

        // Configure permissions
        builder.Property("_permissions")
            .HasColumnName("Permissions")
            .HasConversion(
                permissions => string.Join(',', permissions),
                value => value.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
            .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<List<string>>(
                (c1, c2) => c1!.SequenceEqual(c2!),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList()));

        // Ignore domain events
        builder.Ignore(r => r.DomainEvents);
    }
}
