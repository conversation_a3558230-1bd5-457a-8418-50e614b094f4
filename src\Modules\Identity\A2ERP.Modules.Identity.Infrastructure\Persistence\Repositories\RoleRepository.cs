using A2ERP.Modules.Identity.Domain.Entities;
using A2ERP.Modules.Identity.Domain.Repositories;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;

namespace A2ERP.Modules.Identity.Infrastructure.Persistence.Repositories;

public sealed class RoleRepository : Repository<Role, RoleId>, IRoleRepository
{
    public RoleRepository(IdentityDbContext context) : base(context)
    {
    }

    public async Task<Role?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await DbSet.FirstOrDefaultAsync(r => r.Name == name, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await DbSet.AnyAsync(r => r.Name == name, cancellationToken);
    }

    public async Task<IEnumerable<Role>> GetActiveRolesAsync(CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(r => r.IsActive)
            .ToListAsync(cancellationToken);
    }
}
