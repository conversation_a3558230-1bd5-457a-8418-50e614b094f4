using A2ERP.Modules.Identity.Domain.Entities;
using A2ERP.Modules.Identity.Domain.Repositories;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;

namespace A2ERP.Modules.Identity.Infrastructure.Persistence.Repositories;

public sealed class UserRepository : Repository<User, UserId>, IUserRepository
{
    public UserRepository(IdentityDbContext context) : base(context)
    {
    }

    public async Task<User?> GetByEmailAsync(Email email, CancellationToken cancellationToken = default)
    {
        return await DbSet.FirstOrDefaultAsync(u => u.Email == email, cancellationToken);
    }

    public async Task<bool> ExistsByEmailAsync(Email email, CancellationToken cancellationToken = default)
    {
        return await DbSet.AnyAsync(u => u.Email == email, cancellationToken);
    }

    public async Task<User?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        return await DbSet.FirstOrDefaultAsync(
            u => u.RefreshToken == refreshToken && 
                 u.RefreshTokenExpiryTime > DateTime.UtcNow, 
            cancellationToken);
    }

    public async Task<IEnumerable<User>> GetByRoleIdAsync(RoleId roleId, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(u => u.RoleIds.Contains(roleId))
            .ToListAsync(cancellationToken);
    }
}
