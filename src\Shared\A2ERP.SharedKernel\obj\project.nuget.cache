{"version": 2, "dgSpecHash": "cMqsslznE5Q=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\A2ERP\\src\\Shared\\A2ERP.SharedKernel\\A2ERP.SharedKernel.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\12.2.0\\mediatr.12.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.contracts\\2.0.1\\mediatr.contracts.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"], "logs": []}