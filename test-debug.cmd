@echo off
echo ========================================
echo Debug A2ERP API Startup
echo ========================================

cd src\A2ERP.API

echo.
echo Backing up original Program.cs...
copy Program.cs Program-Original.cs

echo.
echo Using debug Program.cs...
copy Program-Debug.cs Program.cs

echo.
echo Building...
dotnet build

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    echo Restoring original Program.cs...
    copy Program-Original.cs Program.cs
    pause
    exit /b 1
)

echo.
echo Starting debug application...
echo This will show exactly where the application freezes.
echo.

dotnet run --urls "https://localhost:7000;http://localhost:5000"

echo.
echo Restoring original Program.cs...
copy Program-Original.cs Program.cs

pause
