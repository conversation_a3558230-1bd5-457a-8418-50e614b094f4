@echo off
echo ========================================
echo Testing HTTP-Only A2ERP API Version
echo ========================================

echo.
echo Creating HTTP-only Program.cs...
call create-http-only-program.cmd

cd src\A2ERP.API

echo.
echo Building HTTP-only version...
dotnet build --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    echo Restoring original Program.cs...
    copy Program-Original.cs Program.cs
    pause
    exit /b 1
)

echo.
echo Starting HTTP-only application...
echo Navigate to: http://localhost:5000/test
echo.

dotnet run

echo.
echo Restoring original Program.cs...
copy Program-Original.cs Program.cs
del Program-Original.cs

pause
