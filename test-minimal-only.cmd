@echo off
echo ========================================
echo Testing Minimal A2ERP API Only
echo ========================================

cd src\A2ERP.API

echo.
echo Backing up current Program.cs...
if exist Program.cs (
    copy Program.cs Program-Backup.cs
)

echo.
echo This script is deprecated. Use test-minimal-version.cmd instead.
echo.
pause
exit /b 1

echo.
echo Building minimal version...
dotnet build --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    if exist Program-Backup.cs (
        copy Program-Backup.cs Program.cs
    )
    pause
    exit /b 1
)

echo.
echo Starting minimal application...
echo This should start quickly if there are no fundamental issues.
echo.

timeout /t 2 /nobreak > nul
dotnet run

echo.
echo Restoring original Program.cs...
if exist Program-Backup.cs (
    copy Program-Backup.cs Program.cs
    del Program-Backup.cs
)

pause
