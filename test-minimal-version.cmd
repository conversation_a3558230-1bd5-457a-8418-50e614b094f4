@echo off
echo ========================================
echo Testing Minimal A2ERP API Version
echo ========================================

echo.
echo Creating minimal Program.cs...
call create-minimal-program.cmd

cd src\A2ERP.API

echo.
echo Building minimal version...
dotnet build --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    echo Restoring original Program.cs...
    copy Program-Original.cs Program.cs
    pause
    exit /b 1
)

echo.
echo Starting minimal application...
echo This should start quickly if there are no fundamental issues.
echo Navigate to: https://localhost:7000/test
echo.

dotnet run

echo.
echo Restoring original Program.cs...
copy Program-Original.cs Program.cs
del Program-Original.cs

pause
