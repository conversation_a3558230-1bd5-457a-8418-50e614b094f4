@echo off
echo ========================================
echo Test A2ERP API Without Database
echo ========================================

cd src\A2ERP.API

echo.
echo Backing up original Program.cs...
copy Program.cs Program-Original.cs

echo.
echo Using no-database Program.cs...
copy Program-NoDatabase.cs Program.cs

echo.
echo Building...
dotnet build

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    echo Restoring original Program.cs...
    copy Program-Original.cs Program.cs
    pause
    exit /b 1
)

echo.
echo Starting application without database dependencies...
echo Navigate to: https://localhost:7000/test
echo.

dotnet run --urls "https://localhost:7000;http://localhost:5000"

echo.
echo Restoring original Program.cs...
copy Program-Original.cs Program.cs

pause
