@echo off
echo ========================================
echo SQL Server Connection Test
echo ========================================

echo.
echo Testing SQL Server connection...
echo Connection String: Server=.;Database=master;Trusted_Connection=true;TrustServerCertificate=true
echo.

sqlcmd -S . -E -Q "SELECT @@VERSION" -W

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ SQL Server connection successful!
    echo.
    echo Checking if A2ERP database exists...
    sqlcmd -S . -E -Q "SELECT name FROM sys.databases WHERE name = 'A2ERP'" -W
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo You can now run: setup-database.cmd
    ) else (
        echo.
        echo Database check completed. Run setup-database.cmd to create the A2ERP database.
    )
) else (
    echo.
    echo ✗ SQL Server connection failed!
    echo.
    echo Possible solutions:
    echo 1. Make sure SQL Server is installed and running
    echo 2. Check if SQL Server service is started
    echo 3. Verify Windows Authentication is enabled
    echo 4. Try running as Administrator
    echo.
    echo To check SQL Server services:
    echo   services.msc ^(look for SQL Server services^)
    echo.
    echo To install SQL Server Express:
    echo   https://www.microsoft.com/en-us/sql-server/sql-server-downloads
)

echo.
pause
