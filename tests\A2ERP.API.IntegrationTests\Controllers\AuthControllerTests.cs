using A2ERP.Modules.Identity.Application.Commands.LoginUser;
using A2ERP.Modules.Identity.Application.Commands.RegisterUser;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace A2ERP.API.IntegrationTests.Controllers;

public sealed class AuthControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public AuthControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task Register_WithValidData_ShouldReturnOk()
    {
        // Arrange
        var command = new RegisterUserCommand(
            Email: "<EMAIL>",
            Password: "Password123!",
            FirstName: "John",
            LastName: "Doe");

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task Register_WithInvalidEmail_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new RegisterUserCommand(
            Email: "invalid-email",
            Password: "Password123!",
            FirstName: "John",
            LastName: "Doe");

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/register", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task Login_WithValidCredentials_ShouldReturnOk()
    {
        // Arrange - First register a user
        var registerCommand = new RegisterUserCommand(
            Email: "<EMAIL>",
            Password: "Password123!",
            FirstName: "John",
            LastName: "Doe");

        await _client.PostAsJsonAsync("/api/auth/register", registerCommand);

        var loginCommand = new LoginUserCommand(
            Email: "<EMAIL>",
            Password: "Password123!");

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/login", loginCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
        
        // Verify the response contains access token
        var jsonDocument = JsonDocument.Parse(content);
        jsonDocument.RootElement.TryGetProperty("accessToken", out var accessTokenProperty).Should().BeTrue();
        accessTokenProperty.GetString().Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task Login_WithInvalidCredentials_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new LoginUserCommand(
            Email: "<EMAIL>",
            Password: "WrongPassword");

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/login", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }
}
