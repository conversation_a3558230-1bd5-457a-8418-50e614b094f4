<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../src/Modules/Identity/A2ERP.Modules.Identity.Domain/A2ERP.Modules.Identity.Domain.csproj" />
    <ProjectReference Include="../../src/Modules/Identity/A2ERP.Modules.Identity.Application/A2ERP.Modules.Identity.Application.csproj" />
    <ProjectReference Include="../../src/Modules/Identity/A2ERP.Modules.Identity.Infrastructure/A2ERP.Modules.Identity.Infrastructure.csproj" />
  </ItemGroup>

</Project>
