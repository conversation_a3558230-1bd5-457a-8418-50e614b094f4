using A2ERP.Modules.Identity.Domain.ValueObjects;
using FluentAssertions;
using Xunit;

namespace A2ERP.Modules.Identity.UnitTests.Domain;

public sealed class EmailTests
{
    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Create_WithValidEmail_ShouldCreateEmail(string emailAddress)
    {
        // Act
        var email = Email.Create(emailAddress);

        // Assert
        email.Should().NotBeNull();
        email.Value.Should().Be(emailAddress.ToLowerInvariant());
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    [InlineData("invalid-email")]
    [InlineData("@example.com")]
    [InlineData("test@")]
    [InlineData("test.example.com")]
    public void Create_WithInvalidEmail_ShouldThrowArgumentException(string emailAddress)
    {
        // Act & Assert
        var act = () => Email.Create(emailAddress);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Create_WithUpperCaseEmail_ShouldConvertToLowerCase()
    {
        // Arrange
        var emailAddress = "<EMAIL>";

        // Act
        var email = Email.Create(emailAddress);

        // Assert
        email.Value.Should().Be("<EMAIL>");
    }

    [Fact]
    public void Equals_WithSameEmail_ShouldReturnTrue()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.Should().Be(email2);
        (email1 == email2).Should().BeTrue();
    }

    [Fact]
    public void Equals_WithDifferentEmail_ShouldReturnFalse()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.Should().NotBe(email2);
        (email1 != email2).Should().BeTrue();
    }
}
