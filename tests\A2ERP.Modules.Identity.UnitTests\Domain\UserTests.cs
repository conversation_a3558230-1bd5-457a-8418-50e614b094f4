using A2ERP.Modules.Identity.Domain.Entities;
using A2ERP.Modules.Identity.Domain.ValueObjects;
using FluentAssertions;
using Xunit;

namespace A2ERP.Modules.Identity.UnitTests.Domain;

public sealed class UserTests
{
    [Fact]
    public void Create_WithValidData_ShouldCreateUser()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var firstName = "John";
        var lastName = "Doe";
        var passwordHash = "hashedPassword";

        // Act
        var user = User.Create(email, firstName, lastName, passwordHash);

        // Assert
        user.Should().NotBeNull();
        user.Email.Should().Be(email);
        user.FirstName.Should().Be(firstName);
        user.LastName.Should().Be(lastName);
        user.PasswordHash.Should().Be(passwordHash);
        user.IsActive.Should().BeTrue();
        user.EmailConfirmed.Should().BeFalse();
        user.DomainEvents.Should().HaveCount(1);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Create_WithInvalidFirstName_ShouldThrowArgumentException(string firstName)
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var lastName = "Doe";
        var passwordHash = "hashedPassword";

        // Act & Assert
        var act = () => User.Create(email, firstName, lastName, passwordHash);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ChangeEmail_WithValidEmail_ShouldUpdateEmail()
    {
        // Arrange
        var user = CreateTestUser();
        var newEmail = Email.Create("<EMAIL>");

        // Act
        user.ChangeEmail(newEmail);

        // Assert
        user.Email.Should().Be(newEmail);
        user.EmailConfirmed.Should().BeFalse();
        user.DomainEvents.Should().HaveCount(2); // UserCreated + UserEmailChanged
    }

    [Fact]
    public void ChangeEmail_WithSameEmail_ShouldNotRaiseDomainEvent()
    {
        // Arrange
        var user = CreateTestUser();
        var currentEmail = user.Email;

        // Act
        user.ChangeEmail(currentEmail);

        // Assert
        user.Email.Should().Be(currentEmail);
        user.DomainEvents.Should().HaveCount(1); // Only UserCreated
    }

    [Fact]
    public void AddRole_WithValidRoleId_ShouldAddRole()
    {
        // Arrange
        var user = CreateTestUser();
        var roleId = RoleId.CreateUnique();

        // Act
        user.AddRole(roleId);

        // Assert
        user.RoleIds.Should().Contain(roleId);
        user.HasRole(roleId).Should().BeTrue();
    }

    [Fact]
    public void RemoveRole_WithExistingRoleId_ShouldRemoveRole()
    {
        // Arrange
        var user = CreateTestUser();
        var roleId = RoleId.CreateUnique();
        user.AddRole(roleId);

        // Act
        user.RemoveRole(roleId);

        // Assert
        user.RoleIds.Should().NotContain(roleId);
        user.HasRole(roleId).Should().BeFalse();
    }

    private static User CreateTestUser()
    {
        var email = Email.Create("<EMAIL>");
        return User.Create(email, "John", "Doe", "hashedPassword");
    }
}
